(function ($) {
	"use strict";
	
	/* function initializeVersioning() {
		$('.download-addon-widget-wrp').each(function() {
			const $container = $(this);
			const data = JSON.parse($container.attr('data-json-dto'));
			const defaultLanguage = $container.attr('data-default-language') || 'en';
			
			let currentVersion = {
				major: data.major_versions[0].version,
				patch: data.major_versions[0].patch_versions[0].version,
				language: defaultLanguage
			};

			// Initialize dropdowns
			function initializeVersionsDropdown() {
				const $versionsDropdown = $container.find('.versioning-versions');
				const $versionButton = $versionsDropdown.closest('.btn-group');
				$versionsDropdown.empty();
				
				if (!data.major_versions || data.major_versions.length === 0) {
					$versionButton.hide();
					return;
				}
				
				data.major_versions.forEach(majorVersion => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${majorVersion.version}">
							${majorVersion.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.major = majorVersion.version;
						initializePatchVersionsDropdown(majorVersion.version);
						updateDisplay();
					});
					
					$versionsDropdown.append($item);
				});
				
				$versionButton.show();
			}

			function initializePatchVersionsDropdown(majorVersion) {
				const $patchesDropdown = $container.find('.versioning-versions-patches');
				const $patchButton = $patchesDropdown.closest('.btn-group');
				$patchesDropdown.empty();
				
				const patches = data.major_versions.find(mv => mv.version === majorVersion)?.patch_versions || [];
				
				if (patches.length === 0) {
					$patchButton.hide();
					return;
				}
				
				patches.forEach(patch => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${patch.version}">
							${patch.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.patch = patch.version;
						$container.find('.versioning-versions-patches li:first a').addClass('active');
						updateDisplay();
					});
					
					$patchesDropdown.append($item);
				});
				
				$patchButton.show();
			}

			function initializeLanguagesDropdown(patchVersion) {
				const $languagesDropdown = $container.find('.versioning-languages');
				const $languageButton = $languagesDropdown.closest('.btn-group');
				$languagesDropdown.empty();
				
			// /* 	if (!patchVersion || !patchVersion.downloads || patchVersion.downloads.length === 0) {
			// 		$languageButton.hide();
			// 		return;
			// 	}
				
				const languages = new Set();
				patchVersion.downloads.forEach(download => {
					if (!languages.has(download.language)) {
						languages.add(download.language);
						
						const $item = $(`<li data-type="item">
							<a class="dropdown-item" href="#" data-language="${download.language}">
								${getLanguageName(download.language)}
							</a>
						</li>`);
						
						$item.find('a').on('click', function(e) {
							e.preventDefault();
							currentVersion.language = download.language;
							initializeVersionsDropdown();
							updateDisplay();
							$container.find('.versioning-versions li:first a').trigger('click');
						});
						
						$languagesDropdown.append($item);
					}
				});
				
				$languageButton.toggle(languages.size > 0);
			}

			function updateDisplay() {
				const majorVersion = data.major_versions.find(mv => mv.version === currentVersion.major);
				var patchVersion = majorVersion?.patch_versions.find(pv => pv.version === currentVersion.patch);
				
				if( !patchVersion ){
					patchVersion = majorVersion?.patch_versions[0];
				}
				
				if (!majorVersion) return;

				// Update version display
				$container.find('.versioning-selected-parent').text(majorVersion.version);
				$container.find('.versioning-selected-version').text(patchVersion.version);
				$container.find('.versioning-selected-suffix').text(majorVersion.suffix);

				// Update download link
				const download = patchVersion.downloads.find(d => d.language === currentVersion.language);
				if (download) {
					$container.find('.mysolidcam-versioning__link').attr('href', download.url);
					$container.find('.selected-language').text(getLanguageName(download.language));
				}

				// Update MSI link
				if (patchVersion.msi) {
					$container.find('.mysolidcam-versioning-msi').attr('href', patchVersion.msi).show();
				} else {
					$container.find('.mysolidcam-versioning-msi').hide();
				}

				// Update release notes
				if (patchVersion.release_notes) {
					$container.find('.mysolidcam-versioning-selected-release-notes')
						.attr('href', patchVersion.release_notes.url)
						.closest('.version-release-date-wrp').show();
				} else {
					$container.find('.version-release-date-wrp').hide();
				}

				// Update release date
				if (patchVersion.released_date) {
					$container.find('.mysolidcam-versioning-selected-release-date').text(patchVersion.released_date);
				}

				// Refresh dropdowns
				initializeLanguagesDropdown(patchVersion);
				initializePatchVersionsDropdown(currentVersion.major);
				

				// Update active states
				$container.find('.dropdown-item').removeClass('active');
				$container.find(`[data-version="${currentVersion.major}"]`).addClass('active');
				$container.find(`[data-version="${currentVersion.patch}"]`).addClass('active');
				$container.find(`[data-language="${currentVersion.language}"]`).addClass('active');
			}

			function getLanguageName(code) {
				const languages = download_module.languages;
				return languages[code] || code;
			}

			// Initialize
			initializeVersionsDropdown();
			updateDisplay();
		});
	} */
	
	function initializeVersioning() {
		$('.download-addon-widget-wrp').each(function() {
			const $container = $(this);
			const data = JSON.parse($container.attr('data-json-dto'));
			const defaultLanguage = $container.attr('data-default-language') || 'en';
			
			let currentVersion = {
				language: defaultLanguage,
				major: null,
				patch: null
			};
	
			// Initialize Languages Dropdown
			function initializeLanguagesDropdown() {
				const $languagesDropdown = $container.find('.versioning-languages');
				const $languageButton = $languagesDropdown.closest('.btn-group');
				$languagesDropdown.empty();
				
				// Get all unique languages
				const languages = new Set();
				data.major_versions.forEach(majorVersion => {
					majorVersion.patch_versions.forEach(patch => {
						patch.downloads.forEach(download => {
							languages.add(download.language);
						});
					});
				});
				
				languages.forEach(language => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-language="${language}">
							${getLanguageName(language)}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.language = language;
						updateVersionsDropdown(true); // true indicates it should auto-select first version
					});
					
					$languagesDropdown.append($item);
				});
				
				// Auto-select first language if none selected
				if (!currentVersion.language && languages.size > 0) {
					currentVersion.language = languages.values().next().value;
				}
				
				$languageButton.toggle(languages.size > 0);
				updateVersionsDropdown(true);
			}
	
			function updateVersionsDropdown(autoSelect = false) {
				const $versionsDropdown = $container.find('.versioning-versions');
				const $versionButton = $versionsDropdown.closest('.btn-group');
				$versionsDropdown.empty();
				
				// Filter versions for selected language
				const availableVersions = data.major_versions.filter(majorVersion => 
					majorVersion.patch_versions.some(patch => 
						patch.downloads.some(download => 
							download.language === currentVersion.language
						)
					)
				);
				
				if (availableVersions.length === 0) {
					$versionButton.hide();
					return;
				}
				
				availableVersions.forEach(majorVersion => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${majorVersion.version}">
							${majorVersion.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.major = majorVersion.version;
						updatePatchesDropdown(true); // true indicates it should auto-select first patch
					});
					
					$versionsDropdown.append($item);
				});
				
				$versionButton.show();
				
				// Auto-select first version if requested
				if (autoSelect || !currentVersion.major) {
					currentVersion.major = availableVersions[0].version;
					updatePatchesDropdown(true);
				}
			}
	
			function updatePatchesDropdown(autoSelect = false) {
				const $patchesDropdown = $container.find('.versioning-versions-patches');
				const $patchButton = $patchesDropdown.closest('.btn-group');
				$patchesDropdown.empty();
				
				const majorVersion = data.major_versions.find(mv => mv.version === currentVersion.major);
				if (!majorVersion) return;
				
				const availablePatches = majorVersion.patch_versions.filter(patch => 
					patch.downloads.some(download => 
						download.language === currentVersion.language
					)
				);
				
				if (availablePatches.length === 0) {
					$patchButton.hide();
					return;
				}
				
				availablePatches.forEach(patch => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${patch.version}">
							${patch.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.patch = patch.version;
						updateDisplay();
					});
					
					$patchesDropdown.append($item);
				});
				
				$patchButton.show();
				
				// Auto-select first patch if requested
				if (autoSelect || !currentVersion.patch) {
					currentVersion.patch = availablePatches[0].version;
					updateDisplay();
				}
			}
	
			function updateDisplay() {
				const majorVersion = data.major_versions.find(mv => mv.version === currentVersion.major);
				const patchVersion = majorVersion?.patch_versions.find(pv => pv.version === currentVersion.patch);
				
				if (!majorVersion || !patchVersion) return;
	
				// Update version display
				$container.find('.versioning-selected-parent').text(majorVersion.version);
				$container.find('.versioning-selected-version').text(patchVersion.version);
				$container.find('.versioning-selected-suffix').text(majorVersion.suffix);
	
				// Update download link
				const download = patchVersion.downloads.find(d => d.language === currentVersion.language);
				if (download) {
					$container.find('.mysolidcam-versioning__link').attr('href', download.url).show();
					$container.find('.selected-language').text(getLanguageName(currentVersion.language));
				}
	
				// Update MSI link
				if (patchVersion.msi) {
					$container.find('.mysolidcam-versioning-msi').attr('href', patchVersion.msi).show();
				} else {
					$container.find('.mysolidcam-versioning-msi').hide();
				}
	
				// Update release notes and date
				if (patchVersion.release_notes) {
					$container.find('.mysolidcam-versioning-selected-release-notes')
						.attr('href', patchVersion.release_notes.url);
					$container.find('.mysolidcam-versioning-selected-release-date').text(patchVersion.released_date);
					$container.find('.version-release-date-wrp').show();
				} else {
					$container.find('.version-release-date-wrp').hide();
				}
	
				// Update active states
				$container.find('.dropdown-item').removeClass('active');
				$container.find(`[data-language="${currentVersion.language}"]`).addClass('active');
				$container.find(`[data-version="${currentVersion.major}"]`).addClass('active');
				$container.find(`[data-version="${currentVersion.patch}"]`).addClass('active');
			}
	
			function getLanguageName(code) {
				const languages = download_module.languages;
				return languages[code] || code;
			}
	
			// Initialize
			initializeLanguagesDropdown();
		});
	}

	// Initialize on document ready
	$(document).ready(function() {
		initializeVersioning();
	});

})(jQuery);