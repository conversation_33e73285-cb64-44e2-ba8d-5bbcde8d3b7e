jQuery(document).ready(function($) {
	function getDaysInMonth(month, year) {
		return new Date(year, month, 0).getDate();
	}
	function loadEvents(month, year) {
        $.ajax({
            url: calendar_ajax.ajax_url,
            method: 'POST',
            data: {
                action: 'fetch_calendar_events',
                month: month,
                year: year
            },
            success: function(response) {
                $('#calendar-days').empty();
    
                const firstDayOfMonth = new Date(year, month - 1, 1).getDay();
                const daysInMonth = getDaysInMonth(month, year);
                
                
                 // Get the current date
                const currentDate = new Date();
                const currentDay = currentDate.getDate();
                const currentMonth = currentDate.getMonth() + 1; // Current month (1-12)
                const currentYear = currentDate.getFullYear(); // Current year
    
                let row = '<tr>';
                for (let i = 0; i < firstDayOfMonth; i++) {
                    row += '<td class="empty"></td>';
                }
    
                for (let day = 1; day <= daysInMonth; day++) {
                    if ((firstDayOfMonth + day - 1) % 7 === 0 && day > 1) {
                        row += '</tr><tr>';
                    }
    
                    const events = response[day] || [];
                    let eventHTML = '';
    
                    events.forEach(event => {
                        const truncatedTitle = event.title.length > 15 ? event.title.substring(0, 15) + "..." : event.title;
                        eventHTML += `<span class="post-title">${truncatedTitle}</span>`;
                    });
    
                    // Check if there are events, and add a class if true
                    const hasEvents = events.length > 0 ? 'has-events' : '';
                    
                    // Create a div to show the number of events
                    const eventCount = events.length > 0 ? `<div class="event-count">${events.length}</div>` : '';
                    
                     // Add the 'current-day' class if it's the current day
                    const currentDayClass = (day === currentDay && month === currentMonth && year === currentYear) ? 'current-day' : '';
    
                    // Add the tooltip or extra div for the events
                    row += `<td class="day-cell"><div class ="${hasEvents} ${currentDayClass}"><span class="post-data">${eventHTML}</span>
                                ${day}
                                ${eventCount}
                                </div>
                            </td>`;
                }
    
                row += '</tr>';
                $('#calendar-days').append(row);
    
                // Refresh tooltips if using tooltip library (e.g., Bootstrap)
               // $('.day-cell[title]').tooltip();
    
                // Add hover effect to show events in a tooltip or extra div
                $('.day-cell.has-events').hover(function() {
                    // Show events in a tooltip or extra div
                    const eventContent = $(this).data('events');
                    const tooltipDiv = $('<div class="event-tooltip"></div>').html(eventContent);
                    $(this).append(tooltipDiv);
    
                    // Position the tooltip
                    const offset = $(this).offset();
                    tooltipDiv.css({
                        top: offset.top - tooltipDiv.outerHeight() - 10, // Position above the cell
                        left: offset.left + ($(this).outerWidth() - tooltipDiv.outerWidth()) / 2 // Center it
                    });
                }, function() {
                    // Remove the tooltip on mouseout
                    $(this).find('.event-tooltip').remove();
                });
            }
        });
    }


  

	let currentMonth = new Date().getMonth() + 1;
	let currentYear = new Date().getFullYear();

	loadEvents(currentMonth, currentYear);

    $('.next-month, .prev-month').on('click', function(e) {
        e.preventDefault();
        
        // Check if next or previous button was clicked
        if ($(this).hasClass('next-month')) {
            currentMonth++;
            // If month goes beyond December, move to January of the next year
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        } else if ($(this).hasClass('prev-month')) {
            currentMonth--;
            // If month goes before January, move to December of the previous year
            if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }
        }
        
        // Load events for the updated month and year
        loadEvents(currentMonth, currentYear);
    
        // Update the display for the current month and year
        $('.current-month-year').text(new Date(currentYear, currentMonth - 1).toLocaleString('default', { month: 'long', year: 'numeric' }));
    });

});
