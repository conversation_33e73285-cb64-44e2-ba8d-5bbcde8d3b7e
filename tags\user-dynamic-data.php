<?php

    if (!class_exists('User_Dynamic_Data')) {
        class User_Dynamic_Data extends \Elementor\Core\DynamicTags\Tag {
            public function get_name() {
                return 'User-Dynamic-Data';
            }
    
            public function get_title() {
                return 'User / Form Dynamic Data';
            }
    
            public function get_group() {
                return 'site'; // or use other existing groups like 'post', 'archive'
            }
        
            public function get_categories() {
                return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
            }
    
            protected function register_controls() {
                $this->add_control(
                    'selected_option',
                    [
                        'label' => 'Select Option',
                        'type' => \Elementor\Controls_Manager::SELECT,
                        'default' => 'logout',
                        'options' => [
                            'logout' => 'Logout',
                            'account_type' => 'Account Type',
                            'plan' => 'Your Plan',
                        ],
                    ]
                );
            }
    
            public function render() {
                $selected_option = $this->get_settings('selected_option');
				
				$current_user = wp_get_current_user();
				$user_id = $current_user->ID;
				
				$customer_account = get_user_meta($user_id, 'customer_account', true);
				$subscription = esc_html__('No Subscription', 'solidcam');
				
				// Validate the customer account meta
				if (!empty($customer_account) && is_array($customer_account)) {
					$sub_end_date = isset($customer_account['sub_end_date']) ? $customer_account['sub_end_date'] : null;
					
					$sub_end_date = strtotime($sub_end_date);
					$current_date = strtotime(date('d-m-Y'));
					if ($sub_end_date < $current_date) {
						$subscription = esc_html__('Expired', 'solidcam');
					} else {
						$subscription = esc_html__('Subscription', 'solidcam');
					}
				}
				
				$account_type = __('Customer', 'solidcam');
				if( in_array('staff', $current_user->roles) ){
					$account_type = esc_html__('Staff', 'solidcam');
				} else if( in_array('reseller', $current_user->roles) ){
					$account_type = esc_html__('Reseller', 'solidcam');
				} else if( in_array('partner', $current_user->roles) ){
					$account_type = esc_html__('Partner', 'solidcam');
				}
                
                switch ($selected_option) {
                    case 'logout':
                        echo '<a href="' . esc_url( ( function_exists('um_get_core_page' ) ? um_get_core_page( 'logout' ) : '' ) ) . '">' . __('Logout', 'solidcam') . '</a>';
                        break;
                     case 'account_type':
                        echo '<div class="user-plans">' . __('Account Type:', 'solidcam') . ' <span>' . $account_type . '</span></div>';
                        break;
                    case 'plan':
                        echo '<div class="user-plans">' . __('Your Plan:', 'solidcam') . ' <span>' . $subscription . '</span></div>';
                        break;
                    default:
                        echo '<div>' . __('Select an option from the dropdown.', 'solidcam') . '</div>';
                        break;
                }
            }
			
			
        }
    }


?>