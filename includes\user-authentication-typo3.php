<?php
/**
 * SolidCAM Website User Management
 * Handles user registration, authentication, and password recovery
 */

const USER_ROLES = [
	'CUSTOMER' => 'customer',
	'RESELLER' => 'reseller',
	'STAFF' => 'staff',
	'PARTNER' => 'partner'
];

const LEGACY_USER_GROUPS = [
	4 => USER_ROLES['STAFF'],
	5 => USER_ROLES['RESELLER'],
	16 => USER_ROLES['PARTNER']
];

const FORM_IDS = [
	'CUSTOMER_REGISTRATION' => 'customer_registration',
	'RESELLER_PARTNER' => 'reseller_partner',
	'USER_UPDATE' => 'user_update_form'
];

const META_FIELDS = ['company', 'address', 'zip', 'city', 'telephone', 'country', 'state', 'preferred_language', 'dongle_no'];

// Handle Elementor form submissions
add_action('elementor_pro/forms/new_record', 'handle_registration_submission', 10, 2);

function get_form_data($record) {
	$raw_fields = $record->get('fields');
	$form_data = [];
	foreach ($raw_fields as $field) {
		$form_data[$field['id']] = $field['value'];
	}
	return $form_data;
}

function validate_registration($form_data, $handler) {
	$errors = [];
	
	if ($form_data['password'] !== $form_data['repeat_password']) {
		$errors['password'] = __('Passwords do not match. Please try again.', 'solidcam');
	}
	
	if (email_exists($form_data['email'])) {
		$errors['email'] = __('Email already registered. Please use different email or reset password.', 'solidcam');
	}
	
	global $wpdb;
	if ($wpdb->get_row($wpdb->prepare("SELECT * FROM fe_users WHERE email = %s", $form_data['email']))) {
		$errors['email'] = __('Email already registered. Please use different email or reset password.', 'solidcam');
	}
	
	$verify_dongle = verify_dongle_with_solidcam($form_data['dongle_no']);
	if (empty($verify_dongle) || isset($verify_dongle['error'])) {
		$errors['dongle_no'] = __('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam');
	}
	
	foreach ($errors as $field => $message) {
		$handler->add_error($field, $message);
	}
	
	return empty($errors);
}

function get_user_role($account_type) {
	return $account_type ? $account_type ?? $account_type : USER_ROLES['CUSTOMER'];
}

function create_wp_user($form_data, $user_role) {
	$userdata = [
		'user_login' => $form_data['email'],
		'user_email' => $form_data['email'],
		'user_pass' => $form_data['password'],
		'first_name' => $form_data['first_name'],
		'last_name' => $form_data['last_name'],
		'display_name' => "{$form_data['first_name']} {$form_data['last_name']}",
		'role' => $user_role
	];
	
	$user_id = wp_insert_user($userdata);
	if (!is_wp_error($user_id)) {
		foreach (META_FIELDS as $field) {
			if (!empty($form_data[$field])) {
				update_user_meta($user_id, $field, $form_data[$field]);
				if( $field == 'dongle_no' )
					update_user_meta($user_id, 'user_licenses_enabled', [$form_data[$field]]);
					update_user_meta($user_id, 'active_user_license', $form_data[$field]);
			}
		}
		
		if (function_exists('um_fetch_user')) {
			um_fetch_user($user_id);
			UM()->common()->users()->send_activation($user_id, true);
		}
	}
	return $user_id;
}

function handle_registration_submission($record, $handler) {
	$form_name = $record->get_form_settings('form_id');
	$form_data = get_form_data($record);
	
	if (in_array($form_name, [FORM_IDS['CUSTOMER_REGISTRATION'], FORM_IDS['RESELLER_PARTNER']])) {
		if (!validate_registration($form_data, $handler)) {
			return;
		}
		
		$user_role = get_user_role($form_data['account_type'] ?? '');
		$user_id = create_wp_user($form_data, $user_role);
		
		if (is_wp_error($user_id)) {
			$handler->add_error_message(__('Error creating account. Please try again or contact support.', 'solidcam'));
		}
	} elseif ($form_name == FORM_IDS['USER_UPDATE']) {
		handle_user_update($form_data, $handler, $record);
	}
}

function handle_user_update($form_data, $handler, $record) {
	$user_id = get_current_user_id();
	if (!$user_id) {
		$handler->add_error_message(__('User not logged in.', 'solidcam'));
		return;
	}
	
	$user = get_userdata($user_id);
	if (!empty($form_data['password']) && !wp_check_password($form_data['verify_password'], $user->user_pass, $user_id)) {
		$handler->add_error('verify_password', __('Incorrect current password.', 'solidcam'));
		return;
	}
	
	$dongle_no = get_user_meta( $user_id, 'dongle_no', true );
	$dongle_no_updated = false;
	if( isset( $form_data['dongle_no'] ) && $form_data['dongle_no'] && $form_data['dongle_no'] != $dongle_no ){
		
		$verify_dongle = verify_dongle_with_solidcam($form_data['dongle_no']);
		if (empty($verify_dongle) || isset($verify_dongle['error'])) {
			$handler->add_error('dongle_no', __('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam'));
			return;
		}
		
		$dongle_no_updated = true;
	}
	
	
	$userdata = [
		'ID' => $user_id,
		'first_name' => $form_data['first_name'],
		'last_name' => $form_data['last_name'],
		'display_name' => "{$form_data['first_name']} {$form_data['last_name']}"
	];
	
	if (!empty($form_data['password'])) {
		$userdata['user_pass'] = $form_data['password'];
	}
	
	$user_update = wp_update_user($userdata);
	if (is_wp_error($user_update)) {
		$handler->add_error_message(__('Error updating account. Please try again.', 'solidcam'));
		return;
	}
	
	foreach (META_FIELDS as $field) {
		if (isset($form_data[$field])) {
			update_user_meta($user_id, $field, $form_data[$field]);
		}
	}
	// need to change the logic here for replacement 
	
	if( $dongle_no_updated && isset( $verify_dongle ) ){
		update_user_meta($user_id, 'customer_api_response_complete', $verify_dongle);
		update_user_meta($user_id, 'customer_all_licenses', $verify_dongle['customer_all_licenses']);
		update_user_meta($user_id, 'customer_contacts', $verify_dongle['customer_contacts']);
		update_user_meta($user_id, 'customer_modules', $verify_dongle['customer_modules']);
		update_user_meta($user_id, 'customer_account', $verify_dongle['customer_account']);
		update_user_meta($user_id, 'user_licenses_enabled', [$form_data['dongle_no']]);
		update_user_meta($user_id, 'active_user_license', $form_data['dongle_no']);
	}
	
	handle_profile_image_upload($record, $user_id);
}

function handle_profile_image_upload($record, $user_id) {
	$uploaded_files = $record->get('files');
	if (!empty($uploaded_files['profile_image']['url'][0])) {
		update_user_meta($user_id, 'wp_user_avatar', $uploaded_files['profile_image']['url'][0]);
	}
}

// Avatar handling functions
add_filter('get_avatar_url', 'custom_avatar_url', 10, 3);
add_filter('get_avatar', 'custom_avatar_html', 99999, 6);

function custom_avatar_url($url, $id_or_email, $args) {
	$user = get_user_from_identifier($id_or_email);
	if ($user && ($avatar = get_user_meta($user->ID, 'wp_user_avatar', true))) {
		return $avatar;
	}
	return $url;
}

function custom_avatar_html($avatar, $id_or_email, $size, $default, $alt, $args) {
	$user = get_user_from_identifier($id_or_email);
	if ($user && ($avatar_url = get_user_meta($user->ID, 'wp_user_avatar', true))) {
		return sprintf(
			'<img src="%s" alt="%s" width="%d" height="%d" class="avatar avatar-%d photo" />',
			esc_url($avatar_url),
			esc_attr($alt),
			(int)$size,
			(int)$size,
			(int)$size
		);
	}
	return $avatar;
}

function get_user_from_identifier($id_or_email) {
	if (is_numeric($id_or_email)) {
		return get_user_by('id', $id_or_email);
	}
	if (is_string($id_or_email)) {
		return get_user_by('email', $id_or_email);
	}
	if ($id_or_email instanceof WP_User) {
		return $id_or_email;
	}
	return false;
}

// Legacy authentication and migration
add_filter('authenticate', 'typo3_authenticate_user', 99, 3);

function typo3_authenticate_user($user, $username, $password) {
	if (is_a($user, 'WP_User') || empty($username) || empty($password)) {
		return $user;
	}

	global $wpdb;
	$legacy_user = $wpdb->get_row($wpdb->prepare(
		"SELECT * FROM fe_users WHERE username = %s OR email = %s",
		$username, $username
	));

	if (!$legacy_user) {
		return new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid email or password.', 'solidcam'));
	}

	require_once ABSPATH . WPINC . '/class-phpass.php';
	$wp_hasher = new PasswordHash(8, true);

	if (!typo3_verify_password($password, $legacy_user->password) && 
		!$wp_hasher->CheckPassword($password, $legacy_user->password)) {
		return new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid email or password.', 'solidcam'));
	}

	$wp_user = get_user_by('email', $legacy_user->email);
	if (!$wp_user) {
		$wp_user = migrate_legacy_user($legacy_user, $password);
	}

	return $wp_user;
}

function migrate_legacy_user($legacy_user, $password) {
	$user_role = LEGACY_USER_GROUPS[$legacy_user->usergroup] ?? USER_ROLES['CUSTOMER'];
	
	$user_data = [
		'user_login' => $legacy_user->username ?: $legacy_user->email,
		'user_email' => $legacy_user->email,
		'user_pass' => $password,
		'first_name' => $legacy_user->first_name,
		'last_name' => $legacy_user->last_name,
		'display_name' => $legacy_user->name,
		'role' => $user_role
	];

	$user_id = wp_insert_user($user_data);
	if (is_wp_error($user_id)) {
		return new WP_Error('user_creation_failed', __('<strong>ERROR</strong>: Account creation failed. Please contact support.', 'solidcam'));
	}

	migrate_legacy_user_meta($user_id, $legacy_user);
	return get_user_by('id', $user_id);
}

function migrate_legacy_user_meta($user_id, $legacy_user) {
	$user_licenses_enabled = $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ? 
		explode(",", $legacy_user->tx_datamints_mysolidcam_unlocked_licenses) : [];
	$dongle_no = $legacy_user->tx_feuserregisterextend_dongle ?? '';

	$meta_fields = [
		'telephone' => $legacy_user->telephone,
		'address' => $legacy_user->address,
		'city' => $legacy_user->city,
		'zip' => $legacy_user->zip,
		'country' => $legacy_user->country,
		'state' => $legacy_user->tx_solidcamfeuserextensions_state,
		'preferred_language' => $legacy_user->tx_solidcamfeuserextensions_preferred_language,
		'company' => $legacy_user->company,
		'user_licenses_enabled' => $user_licenses_enabled,
		'active_user_license' => $user_licenses_enabled,
		'dongle_no' => $dongle_no
	];

	if ($dongle_no) {
		$decoded_response = verify_dongle_with_solidcam($dongle_no, $user_id);
		$meta_fields = array_merge($meta_fields, [
			'customer_all_licenses' => $decoded_response['customer_all_licenses'] ?? null,
			'customer_contacts' => $decoded_response['customer_contacts'] ?? null,
			'customer_modules' => $decoded_response['customer_modules'] ?? null,
			'customer_account' => $decoded_response['customer_account'] ?? null
		]);
	}

	foreach ($meta_fields as $key => $value) {
		if (!empty($value)) {
			update_user_meta($user_id, $key, $value);
		}
	}
}

function typo3_verify_password($password, $hash) {
	return password_verify($password, $hash);
}

// Password reset handling
add_action('um_reset_password_process_hook', 'check_legacy_user_on_password_reset', 1);

function check_legacy_user_on_password_reset($args) {
	$username = '';
	foreach ($args as $key => $val) {
		if (strstr($key, 'username_b')) {
			$username = trim(sanitize_text_field($val));
			break;
		}
	}

	$user = get_user_by('login', $username) ?? get_user_by('email', $username);
	
	if (!$user) {
		global $wpdb;
		$legacy_user = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM fe_users WHERE email = %s OR username = %s",
			$username, $username
		));

		if ($legacy_user) {
			migrate_legacy_user($legacy_user, wp_generate_password());
			
			if ( username_exists( $username ) ) {
				$data = get_user_by( 'login', $username );
			} elseif ( email_exists( $user ) ) {
				$data = get_user_by( 'email', $username );
			}
			
			if ( isset( $data ) && is_a( $data, '\WP_User' ) ) {
				um_fetch_user( $data->ID );
				UM()->user()->password_reset();
			}
		}
	}
}

// Register custom user roles
add_action('after_switch_theme', 'add_custom_user_roles');

function add_custom_user_roles() {
	foreach (USER_ROLES as $role_key => $role_name) {
		add_role($role_name, ucfirst($role_name), ['read' => true]);
	}
}