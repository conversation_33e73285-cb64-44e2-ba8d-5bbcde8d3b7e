<?php
//render Function to add to Calendar

add_filter( 'get_add_to_calendar', 'render_add_to_calendar', 10, 3);
    
function render_add_to_calendar($post_id,$settings,$show_all) {
    
    if ($show_all === 'yes') {
        $settings['show_ical'] = $settings['show_google'] = $settings['show_outlook']  = $settings['show_microsoft'] = $settings['show_yahoo'] = 'yes';
    }
  
    // Event details
    $title = urlencode(get_the_title($post_id));
    $description = urlencode(get_post_meta($post_id, 'event_description', true) ?: 'No description available.');
    $start_date = get_post_meta($post_id, 'event_start_date', true);
    $end_date = get_post_meta($post_id, 'event_end_date', true) ?: $start_date;
    $start_time = get_post_meta($post_id, 'start_time', true) ?: '00:00';
    $end_time = get_post_meta($post_id, 'end_time', true) ?: '23:59';
    $location = urlencode(strip_tags(html_entity_decode(get_post_meta($post_id, 'location', true) ?: 'No location specified')));

    // Prepare time formats
    $dt_start = gmdate('Ymd\THis\Z', strtotime("$start_date $start_time"));
    $dt_end = gmdate('Ymd\THis\Z', strtotime("$end_date $end_time"));

    // iCal link
    $ical_link = add_query_arg(['action' => 'generate_ical', 'post_id' => $post_id], site_url('/'));

    // Output dropdown button
    echo '<div class="calendar-dropdown">';
    echo '<button class="calendar-toggle"> <i class="fal fa-calendar-plus" aria-hidden="true"></i> Add to the Calendar</button>';
    echo '<div class="calendar-options" style="display: none;">';

    // Output buttons based on settings
    if ($settings['show_ical'] === 'yes') {
        echo '<a href="' . esc_url($ical_link) . '" class="button" target="_blank" rel="noopener"> <i class="fa-brands fa-apple " aria-hidden="true"></i> Download iCal</a><br>';
    }
    if ($settings['show_google'] === 'yes') {
        echo '<a href="https://calendar.google.com/calendar/r/eventedit?text=' . $title . '&dates=' . $dt_start . '/' . $dt_end . '&details=' . $description . '&location=' . $location . '" target="_blank" class="google-calendar-button"> <i class="fa-brands fa-google " aria-hidden="true"></i> Add to Google Calendar</a><br>';
    }
    if ($settings['show_outlook'] === 'yes') {
        echo '<a href="https://outlook.live.com/calendar/0/deeplink/compose?path=/calendar/view/Month&rru=addevent&subject=' . $title . '&startdt=' . $dt_start . '&enddt=' . $dt_end . '&body=' . $description . '&location=' . $location . '" target="_blank" class="outlook-calendar-button"><i class="fa-brands fa-microsoft" aria-hidden="true"></i>  Add to Outlook Calendar</a><br>';
    }
    if ($settings['show_microsoft'] === 'yes') {
        echo '<a href="https://outlook.office.com/calendar/0/deeplink/compose?subject=' . $title . '&startdt=' . $dt_start . '&enddt=' . $dt_end . '&body=' . $description . '&location=' . $location . '" target="_blank" class="microsoft-calendar-button"> <i class="fa-brands fa-microsoft" aria-hidden="true"></i> Add to Microsoft 365 Calendar</a><br>';
    }
    if ($settings['show_yahoo'] === 'yes') {
        echo '<a href="https://calendar.yahoo.com/?v=60&view=d&type=20&title=' . $title . '&start=' . $dt_start . '&end=' . $dt_end . '&desc=' . $description . '&location=' . $location . '" target="_blank" class="yahoo-calendar-button"><i class="fa-brands fa-yahoo" aria-hidden="true"></i> Add to Yahoo Calendar</a><br>';
    }

    echo '</div>'; // Close calendar options
    echo '</div>'; // Close calendar dropdown



}


// Save timestamps on post update
add_action('acf/save_post', 'update_kurs_events_timestamps', 20);

function update_kurs_events_timestamps($post_id) {

	// Check if it's the correct post type
	$post_type = get_post_type($post_id);
	
	if ($post_type == 'event') {
		// Get the ACF fields 'event_date' and 'event_end_date'
		$event_date = get_field('event_start_date', $post_id);
		$event_end_date = get_field('event_end_date', $post_id);
	
		// Check if the fields are not empty and update meta
		if ($event_date) {
			$event_date_timestamp = strtotime($event_date);
			update_post_meta($post_id, 'event_start_date_timestamp', $event_date_timestamp);
		}
	
		if ($event_end_date) {
			$event_end_date_timestamp = strtotime($event_end_date);
			update_post_meta($post_id, 'event_end_date_timestamp', $event_end_date_timestamp);
		}
	}
}


function fetch_calendar_events() {
    

	$month = intval($_POST['month']);
	$year = intval($_POST['year']);
	


	
	$start_date = strtotime("$year-$month-01");
	$end_date = strtotime("$year-$month-" . date('t', $start_date)); // Last day of the month
	
	

	$args = [
		'post_type' => 'event',
		'posts_per_page' => -1,
		'meta_query' => [
			[
				'key' => 'event_start_date_timestamp',
				'value' => [$start_date, $end_date],
				'compare' => 'BETWEEN',
				'type' => 'NUMERIC'
			]
		]
	];

	$events_query = new WP_Query($args);

	$events = [];

	if ($events_query->have_posts()) {
		while ($events_query->have_posts()) {
			$events_query->the_post();
			$day = date('j', strtotime(get_field('event_start_date')));
			$events[$day][] = [
				'title' => get_the_title(),
				'start' => get_field('event_start_date'),
				'end' => get_field('event_end_date'),
				'link' => get_permalink()
			];
		}
	}
	

	wp_send_json($events);
}
add_action('wp_ajax_fetch_calendar_events', 'fetch_calendar_events');
add_action('wp_ajax_nopriv_fetch_calendar_events', 'fetch_calendar_events');




// iCal File Download Functionality
function generate_ical_file($post_id) {
    $post = get_post($post_id);
    
    // Define event details with fallbacks
    $title_html = get_the_title($post_id) ?: 'Untitled Event';
    $title = html_entity_decode($title_html);

    $description_html = get_post_meta($post_id, 'event_description', true) ?: 'No description available.';
    $description = html_entity_decode($description_html);

    $start_date = get_post_meta($post_id, 'event_start_date', true);
    $end_date = get_post_meta($post_id, 'event_end_date', true) ?: $start_date;
    $start_time = get_post_meta($post_id, 'start_time', true) ?: '00:00';
    $end_time = get_post_meta($post_id, 'end_time', true) ?: '23:59';
    
    $location_html = get_post_meta($post_id, 'location', true) ?: 'No location specified';
    $location = strip_tags(html_entity_decode($location_html));

    if (!$start_date) {
        wp_die('A start date is required to generate an iCal file.');
    }

    $dt_start = date('Ymd\THis\Z', strtotime("$start_date $start_time"));
    $dt_end = date('Ymd\THis\Z', strtotime("$end_date $end_time"));
    $dt_stamp = gmdate('Ymd\THis\Z');

    $ical_content = "BEGIN:VCALENDAR\n";
    $ical_content .= "VERSION:2.0\n";
    $ical_content .= "PRODID:-//staging.solidcam.com//Zap Calendar 1.0//EN\n";
    $ical_content .= "CALSCALE:GREGORIAN\n";
    $ical_content .= "METHOD:PUBLISH\n";
    $ical_content .= "BEGIN:VEVENT\n";
    $ical_content .= "SUMMARY:" . addslashes($title) . "\n";
    $ical_content .= "UID:" . uniqid() . "@staging.solidcam.com\n";
    $ical_content .= "SEQUENCE:0\n";
    $ical_content .= "STATUS:CONFIRMED\n";
    $ical_content .= "TRANSP:TRANSPARENT\n";
    $ical_content .= "DTSTART:$dt_start\n";

    if ($end_date && $end_time) {
        $ical_content .= "DTEND:$dt_end\n";
    }

    $ical_content .= "DTSTAMP:$dt_stamp\n";
    $ical_content .= "CATEGORIES:Events\n";
    $ical_content .= "LOCATION:" . addslashes($location) . "\n";
    $ical_content .= "DESCRIPTION:" . addslashes($description) . "\n";
    $ical_content .= "URL:" . get_permalink($post_id) . "\n";
    $ical_content .= "END:VEVENT\n";
    $ical_content .= "END:VCALENDAR";

    header('Content-type: text/calendar; charset=utf-8');
    header('Content-Disposition: attachment; filename="event-' . $post_id . '.ics"');
    echo $ical_content;
    exit;
}


// iCal File Download Logic
add_action('init', function () {
    if (isset($_GET['action']) && $_GET['action'] === 'generate_ical' && isset($_GET['post_id'])) {
        generate_ical_file($_GET['post_id']);
    }
});






//Event Query By Date

function apply_event_date_filter($args) {
    $current_date = date('Ymd');
    $meta_query = [
        'relation' => 'OR',
        [
            'key'     => 'event_start_date',
            'value'   => $current_date,
            'compare' => '>=', // Event starts today or in the future
            'type'    => 'DATE'
        ],
        [
            'key'     => 'event_end_date',
            'value'   => $current_date,
            'compare' => '>=', // Event ends today or in the future
            'type'    => 'DATE'
        ]
    ];

    // Add the meta query to the arguments
    $args['meta_query'] = $meta_query;

    // Order by start date, ascending
    $args['orderby'] = 'meta_value';
    $args['order'] = 'ASC';

    return $args;
}

//Event Query For Unlimted Element
if( !function_exists('custom_events_display_condition_homepage') ){
	function custom_events_display_condition_homepage($args, $widgetData) {
    	// Apply the centralized date filter
    	$args = apply_event_date_filter($args);
    	return $args;
	}
	add_filter("event_filter_by_date", "custom_events_display_condition_homepage", 10, 2);
}

//Event Query For Post Filter Event
if( !function_exists('custom_events_display_condition') ){
	function custom_events_display_condition($query) {
		// Apply the event date filter logic to Elementor's query
		$args = apply_event_date_filter([]);
	
		// Set the modified arguments in the Elementor query object
		$query->set('meta_query', $args['meta_query']);
		$query->set('orderby', $args['orderby']);
		$query->set('order', $args['order']);
	}
	add_action('elementor/query/event_query', 'custom_events_display_condition');
}




?>