<?php

add_filter('acf/load_field/name=choose_license', function ($field) {
	// Initialize the field's choices as an empty array
	$field['choices'] = [];

	// Get the current user ID
	$current_user_id = isset( $_GET['user_id'] ) ? $_GET['user_id'] : get_current_user_id();
		
	if (!$current_user_id) {
		unset( $field['choices'] );
		$field['choices'][] = 'No License Found';
		return $field;
	}

	// Fetch user licenses from user meta or a custom source
	$user_licenses = get_user_meta($current_user_id, 'user_licenses_enabled', true);

	// Ensure $user_licenses is an array
	if (!empty($user_licenses) && is_array($user_licenses)) {
		foreach ($user_licenses as $license_name => $is_enabled) {
			// Add licenses to the choices dropdown only if enabled (or any condition you want)
			$field['choices'][$is_enabled] = $is_enabled;
		}
		$field['value'] = '';
	}

	return $field;
});

add_filter('acf/load_field/name=choose_modules', function ($field) {
	// Initialize the field's choices as an empty array
	
	
	$available_modules = [
		'Add-on' => __('SolidCAM Add-in', 'solidcam'),
		'CAD+CAM' => __('SolidCAM CAD/CAM Suite', 'solidcam'),
		'IVCAM' => __('InventorCAM Add-in', 'solidcam'),
		'SCMV' => __('SolidCAM Maker Version', 'solidcam'),
		'SCSE' => __('SolidCAM Add-In for Solid Edge', 'solidcam'),
		'ICMV' => __('InventorCAM Maker Version', 'solidcam')
	];

	// Get the current user ID
	$current_user_id = isset( $_GET['user_id'] ) ? $_GET['user_id'] : get_current_user_id();
	if (!$current_user_id) {
		return $field;
	}
	
	$user_licenses = get_user_meta($current_user_id, 'user_licenses_enabled', true);
	
	if (empty($user_licenses) || $user_licenses = '') {
		unset( $field['choices'] );
		$field['choices'][] = 'No License Found';
		return $field;
	}
	$user_modules_override = get_user_meta($current_user_id, 'user_modules_override', true);
	$choose_modules = [];
	if (!empty($user_licenses) && is_array($user_licenses)) {
		$choose_modules = get_field('choose_modules', $current_user_id);
		$choose_modules = $user_modules_override[$user_licenses[0]];
	}
	$choose_modules_select = [];
	// Ensure $user_licenses is an array
	if (!empty($choose_modules) && is_array($choose_modules)) {
		// $field['choices'] = [];
		foreach ($choose_modules as $module_key => $module_value) {
			$choose_modules_select[] = $module_value;
		}
		$field['value'] = $choose_modules_select;
	}

	return $field;
});

add_action('wp_ajax_get_modules_by_license', function () {
	// Get the license from the AJAX request
	$license = sanitize_text_field($_POST['license']);
	$user_id = sanitize_text_field($_POST['user_id']);
	
	$current_user_id = $user_id;
	if( $current_user_id ){
		$user_modules_override = get_user_meta($current_user_id, 'user_modules_override', true);
		
		$modules = [];
		if (!empty($user_modules_override) && is_array($user_modules_override)) {
			$modules = $user_modules_override[$license];
		}
		
		// Respond with the modules
		wp_send_json_success(['modules' => $modules]);
	} else {
		wp_send_json_error( $value = null );
	}
	
});

add_action('acf/save_post', function ($post_id) {
	// Ensure it's the right context
	if (strpos($post_id, 'user_') !== 0) {
		return;
	}
	
	$current_user_id = isset( $_POST['user_id'] ) ? $_POST['user_id'] : 0;
	
	if( $current_user_id ){
		$user_modules_override = get_user_meta($current_user_id, 'user_modules_override', true);
		
		if (empty($user_modules_override)) {
			$user_modules_override = [];
		}
		
		// Get the selected license
		$selected_license = get_field('choose_license', $post_id);
		$choose_modules = get_field('choose_modules', $post_id);
		
		$user_modules_override[$selected_license] = $choose_modules;
		
		// Save the modules to the choose_modules field
		update_field('user_modules_override', $user_modules_override, $post_id);
	}
});


// Handle Remove License AJAX
add_action('wp_ajax_remove_license', 'remove_license_callback');
function remove_license_callback() {
	// Check nonce for security
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	// Validate license key
	if (empty($_POST['license'])) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}
	$license = sanitize_text_field($_POST['license']);

	// Get current user and validate
	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to access this functionality.', 'solidcam'));
		return;
	}
	$user_id = $current_user->ID;

	// Fetch user-enabled licenses
	$user_licenses_enabled = get_user_meta($user_id, 'user_licenses_enabled', true);

	// Ensure licenses are an array
	if (!is_array($user_licenses_enabled)) {
		wp_send_json_error(__('No licenses are currently enabled for this user.', 'solidcam'));
		return;
	}

	// Check if the license exists in the user's enabled licenses
	if (!in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('The provided license key is not currently enabled.', 'solidcam'));
		return;
	}

	// Remove the license from the user's enabled licenses
	$updated_licenses = array_filter($user_licenses_enabled, function ($enabled_license) use ($license) {
		return $enabled_license !== $license;
	});

	$update_result = update_user_meta($user_id, 'user_licenses_enabled', $updated_licenses);

	// Check if the meta update was successful
	if ($update_result) {
		wp_send_json_success(__('License removed successfully.', 'solidcam'));
	} else {
		wp_send_json_error(__('Failed to remove the license. Please try again.', 'solidcam'));
	}
}

// Handle Add License AJAX
add_action('wp_ajax_add_license', 'add_license_callback');
function add_license_callback() {
	// Check nonce for security
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	// Validate license key
	if (empty($_POST['license'])) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}
	$license = sanitize_text_field($_POST['license']);

	// Get current user and validate
	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to access this functionality.', 'solidcam'));
		return;
	}
	$user_id = $current_user->ID;

	// Fetch user metadata
	$customer_modules = get_user_meta($user_id, 'customer_modules', true);
	$user_licenses_enabled = get_user_meta($user_id, 'user_licenses_enabled', true);

	// Initialize the user's license array if not set
	if (!is_array($user_licenses_enabled)) {
		$user_licenses_enabled = [];
	}

	// Check if the license is already unlocked
	if (in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('The given key is already unlocked.', 'solidcam'));
		return;
	}

	// Find the license data in customer modules
	if (!is_array($customer_modules)) {
		wp_send_json_error(__('No customer modules available for the current user.', 'solidcam'));
		return;
	}

	$matched_array = array_filter($customer_modules, function ($sub_array) use ($license) {
		return isset($sub_array['license_number']) && $sub_array['license_number'] == $license;
	});

	// Validate if license exists in the user's modules
	if (empty($matched_array)) {
		wp_send_json_error(__('The provided license key does not match any available modules.', 'solidcam'));
		return;
	}

	$matched_license_data = reset($matched_array); // Get the first matching license

	// Add the license to the user's enabled licenses
	$user_licenses_enabled[] = $matched_license_data['license_number'];
	$update_result = update_user_meta($user_id, 'user_licenses_enabled', $user_licenses_enabled);
	update_user_meta($user_id, 'active_user_license', $matched_license_data['license_number']);
	

	// Check if the meta update was successful
	if ($update_result) {
		wp_send_json_success(__('License registered successfully.', 'solidcam'));
	} else {
		wp_send_json_error(__('Failed to register the license. Please try again.', 'solidcam'));
	}
}


// Handle Update Active License AJAX
add_action('wp_ajax_update_active_license', 'update_active_license_callback');
function update_active_license_callback() {
	// Check nonce for security
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	// Validate license key
	if (empty($_POST['license'])) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}
	$license = sanitize_text_field($_POST['license']);

	// Get current user and validate
	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to update the license.', 'solidcam'));
		return;
	}
	$user_id = $current_user->ID;

	// Fetch user-enabled licenses
	$user_licenses_enabled = get_user_meta($user_id, 'user_licenses_enabled', true);

	// Ensure licenses are an array
	if (!is_array($user_licenses_enabled) || !in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('The selected license is not valid.', 'solidcam'));
		return;
	}

	// Update the active_user_license meta field
	update_user_meta($user_id, 'active_user_license', $license);

	wp_send_json_success(__('Active license updated successfully.', 'solidcam'));
}

add_action( 'elementor/element/icon-list/section_icon/before_section_end', 'enable_icon_list_field_user_roles', 100, 2 );
/**
 * enable_icon_list_field_user_roles
 * @param $element
 * @param $args
 */
function enable_icon_list_field_user_roles( $element, $args ) {
	$elementor = \Elementor\Plugin::instance();
	$control_data = $elementor->controls_manager->get_control_from_stack( $element->get_name(), 'icon_list' );

	if ( is_wp_error( $control_data ) ) {
		return;
	}
	
	$tmp = new \Elementor\Repeater();
	
	if ( ! function_exists( 'get_editable_roles' ) ) {
		require_once ABSPATH . 'wp-admin/includes/user.php';
	}
	
	// Get all available roles in WordPress
	$roles = get_editable_roles();
	
	// Prepare an array of roles for the options, with an empty role added at the top
	$role_options = [ '' => esc_html__( '-- Select Role --', 'elementor' ) ]; // Empty role option
	foreach ( $roles as $role_key => $role_data ) {
		$role_options[ $role_key ] = esc_html__( $role_data['name'], 'elementor' );
	}
	
	// Add the switcher control to enable/disable role filtering
	$tmp->add_control(
		'enable_role_filter',
		[
			'label' => esc_html__( 'Enable Role Filter', 'elementor' ),
			'type' => \Elementor\Controls_Manager::SWITCHER,
			'label_off' => esc_html__( 'Off', 'elementor' ),
			'label_on' => esc_html__( 'On', 'elementor' ),
			'default' => 'no', // Default is off
			'separator' => 'before',
		]
	);
	
	// Add the dropdown control for choosing roles
	$tmp->add_control(
		'choose_role',
		[
			'label' => esc_html__( 'Choose Role', 'elementor' ),
			'type' => \Elementor\Controls_Manager::SELECT,
			'options' => $role_options, // Dynamic roles with empty option
			'default' => '', // Default is empty (no role selected)
			'condition' => [
				'enable_role_filter' => 'yes', // Only show when the switcher is enabled
			],
			'separator' => 'after',
		]
	);


	$pattern_field = $tmp->get_controls();
	$enable_role_filter = $pattern_field['enable_role_filter'];
	$choose_role = $pattern_field['choose_role'];

	// insert new class field in advanced tab before field ID control
	$new_order = [];
	foreach ( $control_data['fields'] as $field_key => $field ) {
		if ( 'link' == $field['name'] ) {
			$new_order[ $field_key ] = $field;
			$new_order['enable_role_filter'] = $enable_role_filter;
			$new_order['choose_role'] = $choose_role;
		} else {
			$new_order[ $field_key ] = $field;
		}
		$new_order[ $field_key ] = $field;
	}
	$control_data['fields'] = $new_order;

	$element->update_control( 'icon_list', $control_data );
}

add_action('elementor/frontend/widget/before_render', 'custom_override_icon_list_settings_based_on_roles', 10, 1);
function custom_override_icon_list_settings_based_on_roles( $widget ) {
	// Check if the widget type is icon-list
	if ( 'icon-list' == $widget->get_name() ) {
		
		// Get the current user
		$current_user = wp_get_current_user();
		
		// Get the current widget settings
		$settings = $widget->get_settings();
		
		// Check if CSS class 'user-data-wrp' is set
		$css_class_set = $settings['_css_classes'] ? explode( " ", $settings['_css_classes'] ) : [];
		
		if ( !empty( $css_class_set ) && in_array( 'user-data-wrp', $css_class_set ) ) {
			if ( isset( $settings['icon_list'] ) && is_array( $settings['icon_list'] ) ) {
					
				// Use `array_filter` to filter items based on role
				$icon_list = array_filter( $settings['icon_list'], function( $item ) use ( $current_user ) {
					// Check if role filtering is enabled
					if ( isset( $item['enable_role_filter'] ) && 'yes' === $item['enable_role_filter'] ) {
						// Get the chosen role from settings
						$chosen_role = isset( $item['choose_role'] ) ? $item['choose_role'] : '';
					
						// If a role is chosen, exclude items if the user doesn't have that role
						if ( ! empty( $chosen_role ) && ! in_array( $chosen_role, $current_user->roles ) ) {
							return false; // Exclude the item
						}
					}
					return true; // Include the item
				});

				// Update the widget settings
				$widget->set_settings( 'icon_list', $icon_list );
			}
		}
	}
}
